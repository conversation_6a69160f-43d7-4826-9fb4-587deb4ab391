<%= content_for :head do %>
  <meta name="patient-id" content="<%= @patient.id %>">
<% end %>

  <div class="flex-1 pt-2 px-2 patient-left-details">
    <div class="">
      <div class="flex flex-col gap-5 px-6">
        <div class="grid grid-cols-2 gap-5">
          <%= render "admin/patients/payment_plans" %>
          <%= render "admin/patients/accessibility_alerts" %>
        </div>
        <div class="grid grid-cols-3 gap-5">
          <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-4 shadow-[0_2px_10px_rgba(0,0,0,0.03)] border border-gray-100 flex flex-col">
            <div class="flex items-center mb-4">
              <div class="w-7 h-7 rounded-full bg-blue-100 flex items-center justify-center mr-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user h-4 w-4 text-blue-600">
                  <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                  <circle cx="12" cy="7" r="4"></circle>
                </svg>
              </div>
              <h3 class="text-[14px] font-medium text-gray-800 tracking-tight">Personal Details</h3></div>
            <div class="space-y-3" id="patient-details-form" data-patient-id="<%= @patient.id %>">
              <div>
                <label class="text-[12px] font-medium text-gray-500 mb-1 block">Title</label>
                <div class="dropdown-field border border-gray-200 rounded-md px-3 py-1.5 bg-white transition-colors duration-300" data-field="title">
                  <select name="patient[title]" class="title-select w-full text-[14px] font-medium text-gray-800 bg-transparent border-0 focus:outline-none focus:ring-0">
                    <option value="">-</option>
                    <option value="Mr" <%= @patient.title == 'Mr' ? 'selected' : '' %>>Mr</option>
                    <option value="Mrs" <%= @patient.title == 'Mrs' ? 'selected' : '' %>>Mrs</option>
                    <option value="Miss" <%= @patient.title == 'Miss' ? 'selected' : '' %>>Miss</option>
                    <option value="Ms" <%= @patient.title == 'Ms' ? 'selected' : '' %>>Ms</option>
                    <option value="Dr" <%= @patient.title == 'Dr' ? 'selected' : '' %>>Dr</option>
                  </select>
                </div>
              </div>
              
              <div>
                <label class="text-[12px] font-medium text-gray-500 mb-1 block">First name</label>
                <div class="editable-field border border-gray-200 rounded-md px-3 py-1.5 bg-white transition-colors duration-300" data-field="first_name">
                  <span class="field-display text-[14px] <%= @patient.first_name.present? ? 'font-medium text-gray-800' : 'text-gray-400' %>"><%= @patient.first_name.present? ? @patient.first_name : '-' %></span>
                  <div class="field-edit hidden w-full">
                    <input type="text" name="patient[first_name]" class="w-full text-[14px] font-medium text-gray-800 bg-transparent border-0 focus:outline-none focus:ring-0" value="<%= @patient.first_name %>">
                  </div>
                </div>
              </div>
              
              <div>
                <label class="text-[12px] font-medium text-gray-500 mb-1 block">Last name</label>
                <div class="editable-field border border-gray-200 rounded-md px-3 py-1.5 bg-white transition-colors duration-300" data-field="last_name">
                  <span class="field-display text-[14px] <%= @patient.last_name.present? ? 'font-medium text-gray-800' : 'text-gray-400' %>"><%= @patient.last_name.present? ? @patient.last_name : '-' %></span>
                  <div class="field-edit hidden w-full">
                    <input type="text" name="patient[last_name]" class="w-full text-[14px] font-medium text-gray-800 bg-transparent border-0 focus:outline-none focus:ring-0" value="<%= @patient.last_name %>">
                  </div>
                </div>
              </div>
              
              <div>
                <label class="text-[12px] font-medium text-gray-500 mb-1 block">Middle name</label>
                <div class="editable-field border border-gray-200 rounded-md px-3 py-1.5 bg-white transition-colors duration-300" data-field="middle_name">
                  <span class="field-display text-[14px] <%= @patient.middle_name.present? ? 'font-medium text-gray-800' : 'text-gray-400' %>"><%= @patient.middle_name.present? ? @patient.middle_name : '-' %></span>
                  <div class="field-edit hidden w-full">
                    <input type="text" name="patient[middle_name]" class="w-full text-[14px] font-medium text-gray-800 bg-transparent border-0 focus:outline-none focus:ring-0" value="<%= @patient.middle_name %>">
                  </div>
                </div>
              </div>
              
              <div>
                <label class="text-[12px] font-medium text-gray-500 mb-1 block">Previous last name</label>
                <div class="editable-field border border-gray-200 rounded-md px-3 py-1.5 bg-white transition-colors duration-300" data-field="previous_last_name">
                  <span class="field-display text-[14px] <%= @patient.previous_last_name.present? ? 'font-medium text-gray-800' : 'text-gray-400' %>"><%= @patient.previous_last_name.present? ? @patient.previous_last_name : '-' %></span>
                  <div class="field-edit hidden w-full">
                    <input type="text" name="patient[previous_last_name]" class="w-full text-[14px] font-medium text-gray-800 bg-transparent border-0 focus:outline-none focus:ring-0" value="<%= @patient.previous_last_name %>">
                  </div>
                </div>
              </div>
              
              <div>
                <label class="text-[12px] font-medium text-gray-500 mb-1 block">Preferred name</label>
                <div class="editable-field border border-gray-200 rounded-md px-3 py-1.5 bg-white transition-colors duration-300" data-field="preferred_name">
                  <span class="field-display text-[14px] <%= @patient.preferred_name.present? ? 'font-medium text-gray-800' : 'text-gray-400' %>"><%= @patient.preferred_name.present? ? @patient.preferred_name : '-' %></span>
                  <div class="field-edit hidden w-full">
                    <input type="text" name="patient[preferred_name]" class="w-full text-[14px] font-medium text-gray-800 bg-transparent border-0 focus:outline-none focus:ring-0" value="<%= @patient.preferred_name %>">
                  </div>
                </div>
              </div>
              
              <div>
                <label class="text-[12px] font-medium text-gray-500 mb-1 block">Date of birth</label>
                <div class="date-picker-field border border-gray-200 rounded-md px-3 py-1.5 bg-white transition-colors duration-300" data-field="date_of_birth">
                  <div class="flex items-center w-full">
                    <input type="date" name="patient[date_of_birth]" class="date-picker flex-grow text-[14px] font-medium text-gray-800 bg-transparent border-0 focus:outline-none focus:ring-0" value="<%= @patient.date_of_birth ? @patient.date_of_birth.strftime('%Y-%m-%d') : '' %>">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="calendar-icon h-4 w-4 text-gray-400 cursor-pointer">
                      <rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect>
                      <line x1="16" x2="16" y1="2" y2="6"></line>
                      <line x1="8" x2="8" y1="2" y2="6"></line>
                      <line x1="3" x2="21" y1="10" y2="10"></line>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-4 shadow-[0_2px_10px_rgba(0,0,0,0.03)] border border-gray-100 flex flex-col">
            <div class="flex items-center mb-4">
              <div class="w-7 h-7 rounded-full bg-green-100 flex items-center justify-center mr-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-phone h-4 w-4 text-green-600">
                  <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                </svg>
              </div>
              <h3 class="text-[14px] font-medium text-gray-800 tracking-tight">Contact Details</h3></div>
            <div class="space-y-3">
              <div>
                <label class="text-[12px] font-medium text-gray-500 mb-1 block">Email address</label>
                <div class="editable-field border border-gray-200 rounded-md px-3 py-1.5 bg-white transition-colors duration-300" data-field="email">
                  <span class="field-display text-[14px] font-medium text-gray-800"><%= @patient.email || '-' %></span>
                  <div class="field-edit hidden w-full">
                    <input type="email" name="patient[email]" class="w-full text-[14px] font-medium text-gray-800 bg-transparent border-0 focus:outline-none focus:ring-0" value="<%= @patient.email %>">
                  </div>
                </div>
              </div>
              <div>
                <label class="text-[12px] font-medium text-gray-500 mb-1 block">Phone number</label>
                <%= render 'admin/shared/international_phone_input',
                    field_name: 'mobile_phone',
                    field_value: @patient.mobile_phone,
                    label: 'Phone number' %>
              </div>
              <div>
                <label class="text-[12px] font-medium text-gray-500 mb-1 block">Alternative phone</label>
                <%= render 'admin/shared/international_phone_input',
                    field_name: 'alternative_phone',
                    field_value: @patient.alternative_phone,
                    label: 'Alternative phone' %>
              </div>
              <div>
                <label class="text-[12px] font-medium text-gray-500 mb-1 block">Work number</label>
                <%= render 'admin/shared/international_phone_input',
                    field_name: 'work_phone',
                    field_value: @patient.work_phone,
                    label: 'Work number' %>
              </div>
              <div>
                <label class="text-[12px] font-medium text-gray-500 mb-1 block">Emergency Contact Name</label>
                <div class="editable-field border border-gray-200 rounded-md px-3 py-1.5 bg-white transition-colors duration-300" data-field="emergency_contact_name">
                  <span class="field-display text-[14px] <%= @patient.emergency_contact_name.present? ? 'font-medium text-gray-800' : 'text-gray-400' %>"><%= @patient.emergency_contact_name.present? ? @patient.emergency_contact_name : '-' %></span>
                  <div class="field-edit hidden w-full">
                    <input type="text" name="patient[emergency_contact_name]" class="w-full text-[14px] font-medium text-gray-800 bg-transparent border-0 focus:outline-none focus:ring-0" value="<%= @patient.emergency_contact_name %>">
                  </div>
                </div>
              </div>
              <div>
                <label class="text-[12px] font-medium text-gray-500 mb-1 block">Emergency Contact Number</label>
                <%= render 'admin/shared/international_phone_input',
                    field_name: 'emergency_contact_number',
                    field_value: @patient.emergency_contact_number,
                    label: 'Emergency Contact Number' %>
              </div>
            </div>
          </div>
          <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-4 shadow-[0_2px_10px_rgba(0,0,0,0.03)] border border-gray-100 flex flex-col">
            <div class="flex items-center mb-4">
              <div class="w-7 h-7 rounded-full bg-blue-100 flex items-center justify-center mr-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-home h-4 w-4 text-blue-600">
                  <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                  <polyline points="9 22 9 12 15 12 15 22"></polyline>
                </svg>
              </div>
              <h3 class="text-[14px] font-medium text-gray-800 tracking-tight">Patient Address</h3></div>
            <div class="space-y-3">
              <div><label class="text-[12px] font-medium text-gray-500 mb-1 block">Postcode lookup</label>
                <div class="flex items-center border border-gray-200 rounded-md px-3 py-1.5 bg-white relative">
                  <input id="postcode-lookup-input" placeholder="Search by postcode" class="text-[14px] font-medium text-gray-800 w-full outline-none" type="text" value="<%= @patient.postcode %>">
                  <button id="postcode-lookup-search" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 rounded-md h-6 w-6 p-0 ml-1 text-gray-500 hover:text-gray-700 hover:bg-gray-100/80">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search">
                      <circle cx="11" cy="11" r="8"></circle>
                      <path d="m21 21-4.3-4.3"></path>
                    </svg>
                  </button>
                </div>
              </div>
              <div><label class="text-[12px] font-medium text-gray-500 mb-1 block">Address line 1</label>
                <div class="flex items-center border border-gray-200 rounded-md px-3 py-1.5 bg-white" data-field="address_line_1">
                  <input type="text" class="text-[14px] font-medium text-gray-800 w-full outline-none address-field" data-field-name="address_line_1" value="<%= @patient.address_line_1 || '' %>">
                </div>
              </div>
              <div><label class="text-[12px] font-medium text-gray-500 mb-1 block">Address line 2</label>
                <div class="flex items-center border border-gray-200 rounded-md px-3 py-1.5 bg-white" data-field="address_line_2">
                  <input type="text" class="text-[14px] font-medium text-gray-800 w-full outline-none address-field" data-field-name="address_line_2" value="<%= @patient.address_line_2 || '' %>">
                </div>
              </div>
              <div><label class="text-[12px] font-medium text-gray-500 mb-1 block">City</label>
                <div class="flex items-center border border-gray-200 rounded-md px-3 py-1.5 bg-white" data-field="city">
                  <input type="text" class="text-[14px] font-medium text-gray-800 w-full outline-none address-field" data-field-name="city" value="<%= @patient.city || '' %>">
                </div>
              </div>
              <div><label class="text-[12px] font-medium text-gray-500 mb-1 block">County</label>
                <div class="flex items-center border border-gray-200 rounded-md px-3 py-1.5 bg-white" data-field="county">
                  <input type="text" class="text-[14px] font-medium text-gray-800 w-full outline-none address-field" data-field-name="county" value="<%= @patient.county || '' %>">
                </div>
              </div>
              <div><label class="text-[12px] font-medium text-gray-500 mb-1 block">Postcode</label>
                <div class="flex items-center border border-gray-200 rounded-md px-3 py-1.5 bg-white" data-field="postcode">
                  <input type="text" class="text-[14px] font-medium text-gray-800 w-full outline-none address-field" data-field-name="postcode" value="<%= @patient.postcode || '' %>">
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="grid grid-cols-2 gap-5">
          <%= render 'admin/patients/linked_family_section' %>
          <div class="flex flex-col gap-5">
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-4 shadow-[0_2px_10px_rgba(0,0,0,0.03)] border border-gray-100 flex flex-col min-h-[200px]">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-[14px] font-medium text-gray-800 tracking-tight">Assigned team</h3>
                <div class="flex items-center gap-2">
                  <button id="open-assigned-team-modal" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border hover:text-accent-foreground h-7 w-7 p-0 rounded-full bg-blue-100 text-blue-600 hover:bg-blue-200 border-blue-200">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3.5 w-3.5">
                      <path d="M5 12h14"></path>
                      <path d="M12 5v14"></path>
                    </svg>
                  </button>
                  <button id="edit-team-members" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 h-7 w-7 p-0 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100/80">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-square-pen h-3.5 w-3.5">
                      <path d="M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                      <path d="M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z"></path>
                    </svg>
                  </button>
                </div>
              </div>
              <!-- Team members list -->
              <%= render 'admin/patients/team_members', patient: @patient %>
            </div>
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-4 shadow-[0_2px_10px_rgba(0,0,0,0.03)] border border-gray-100 flex flex-col min-h-[200px]">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-[14px] font-medium text-gray-800 tracking-tight">Assigned practices</h3>
                <div class="flex items-center gap-2">
                  <button id="open-assigned-practices-modal" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border hover:text-accent-foreground h-7 w-7 p-0 rounded-full bg-blue-100 text-blue-600 hover:bg-blue-200 border-blue-200">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3.5 w-3.5">
                      <path d="M5 12h14"></path>
                      <path d="M12 5v14"></path>
                    </svg>
                  </button>
                  <button id="edit-assigned-practices" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 h-7 w-7 p-0 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100/80">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-square-pen h-3.5 w-3.5">
                      <path d="M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                      <path d="M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z"></path>
                    </svg>
                  </button>
                </div>
              </div>
              <!-- Practice members list -->
              <%= render 'admin/patients/practice_members', patient: @patient %>
            </div>
          </div>
        </div>
      </div>
      <style>
          @keyframes apple-edit {
              0% {
                  transform: translate(0) scale(1);
              }
              25% {
                  transform: translate(-2px, 0) scale(1.05);
              }
              50% {
                  transform: translate(0, 0) scale(1);
              }
              75% {
                  transform: translate(2px, 0) scale(1.05);
              }
              100% {
                  transform: translate(0) scale(1);
              }
          }

          .apple-edit-animation {
              animation: apple-edit 1.2s cubic-bezier(0.25, 0.1, 0.25, 1) infinite;
          }

          .apple-edit-animation:hover {
              animation-play-state: paused;
          }
      </style>
    </div>
  </div>
  <div class="w-[480px] bg-white/90 backdrop-blur-xl overflow-auto rounded-3xl shadow-[0_10px_40px_-15px_rgba(0,0,0,0.1)] border border-white/40 ml-4 mt-2 hover:shadow-[0_15px_50px_-12px_rgba(0,0,0,0.12)] patient-right-details">
    <div class="p-4 h-full">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-[14px] font-medium text-gray-800 tracking-tight">Patient notes</h3>
        <div class="flex gap-2">
          <button id="toggle-archived-notes" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border hover:text-accent-foreground h-8 w-8 rounded-full bg-red-100 text-red-600 hover:bg-red-200 border-red-200" data-showing-archived="false" title="Show archived notes">
            <i class="fa-regular fa-box-archive h-4 w-4"></i>
          </button>
          <button id="new-patient-note-btn" onclick="openNewNoteModal()" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border hover:text-accent-foreground px-3 h-8 rounded-full bg-blue-100 text-blue-600 hover:bg-blue-200 border-blue-200">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-3.5 w-3.5 mr-1">
              <path d="M5 12h14"></path>
              <path d="M12 5v14"></path>
            </svg>
            <span class="text-[13px]">New</span></button>
        </div>
      </div>
      <div id="patient-notes-container" class="space-y-6 pb-6">
        <% @notes.each do |note| %>
          <%= render partial: "admin/patients/information/note", locals: { note: note } %>
        <% end %>
      </div>
    </div>
  </div>