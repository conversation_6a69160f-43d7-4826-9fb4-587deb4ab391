<div class="flex-1 pt-2 px-2">
  <div class="h-full p-4 overflow-hidden">
    <div class="grid grid-cols-4 gap-4 w-full">
      <div class="col-span-1">
        <div class="bg-white rounded-lg p-4 shadow-sm h-full border border-gray-100 mb-4">
          <div class="flex items-center mb-4">
            <div class="w-7 h-7 rounded-full bg-red-100 flex items-center justify-center mr-2">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user-check h-4 w-4 text-red-600">
                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                <circle cx="9" cy="7" r="4"></circle>
                <polyline points="16 11 18 13 22 9"></polyline>
              </svg>
            </div>
            <h3 class="text-[15px] font-semibold text-gray-800">GP Information</h3></div>
          <div class="space-y-3" id="gp-information-form" data-patient-id="<%= @patient.id %>">
            <div><label class="text-[12px] font-medium text-gray-500 mb-1 block">Search GP</label>
              <div class="flex items-center border border-gray-200 rounded-md px-3 py-1.5 bg-white relative">
                <input id="gp-search-input" placeholder="Search by GP name or practice" class="text-[14px] font-medium text-gray-800 w-full outline-none" type="text">
                <button id="gp-search-button" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 rounded-md h-6 w-6 p-0 ml-1 text-gray-500 hover:text-gray-700 hover:bg-gray-100/80">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search">
                    <circle cx="11" cy="11" r="8"></circle>
                    <path d="m21 21-4.3-4.3"></path>
                  </svg>
                </button>
              </div>
            </div>
            <div><label class="text-[12px] font-medium text-gray-500 mb-1 block">Practice name</label>
              <div class="border border-gray-200 rounded-md px-3 py-1.5 bg-white">
                <span id="gp-practice-name" class="text-[14px]"><%= @patient.patient_gp&.practice_name || '-' %></span></div>
            </div>
            <div><label class="text-[12px] font-medium text-gray-500 mb-1 block">GP name</label>
              <div class="border border-gray-200 rounded-md px-3 py-1.5 bg-white">
                <span id="gp-name" class="text-[14px]"><%= @patient.patient_gp&.name || '-' %></span></div>
            </div>
            <div class="pt-2 border-t border-gray-100">
              <label class="text-[12px] font-medium text-gray-500 mb-1 block">Address line 1</label>
              <div class="border border-gray-200 rounded-md px-3 py-1.5 bg-white">
                <span id="gp-address-line-1" class="text-[14px]"><%= @patient.patient_gp&.address_line_1 || '-' %></span></div>
            </div>
            <div><label class="text-[12px] font-medium text-gray-500 mb-1 block">Address line 2</label>
              <div class="border border-gray-200 rounded-md px-3 py-1.5 bg-white">
                <span id="gp-address-line-2" class="text-[14px]"><%= @patient.patient_gp&.address_line_2 || '-' %></span></div>
            </div>
            <div><label class="text-[12px] font-medium text-gray-500 mb-1 block">Town/City</label>
              <div class="border border-gray-200 rounded-md px-3 py-1.5 bg-white">
                <span id="gp-city" class="text-[14px]"><%= @patient.patient_gp&.city || '-' %></span></div>
            </div>
            <div><label class="text-[12px] font-medium text-gray-500 mb-1 block">County</label>
              <div class="border border-gray-200 rounded-md px-3 py-1.5 bg-white">
                <span id="gp-county" class="text-[14px]"><%= @patient.patient_gp&.county || '-' %></span></div>
            </div>
            <div><label class="text-[12px] font-medium text-gray-500 mb-1 block">Postcode</label>
              <div class="border border-gray-200 rounded-md px-3 py-1.5 bg-white">
                <span id="gp-postcode" class="text-[14px]"><%= @patient.patient_gp&.postcode || '-' %></span></div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-span-1">
        <div class="bg-white rounded-lg p-4 shadow-sm h-full border border-gray-100 mb-4">
          <div class="flex items-center mb-4">
            <div class="w-7 h-7 rounded-full bg-cyan-100 flex items-center justify-center mr-2">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-school h-4 w-4 text-cyan-600">
                <path d="M14 22v-4a2 2 0 1 0-4 0v4"></path>
                <path d="m18 10 4 2v8a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-8l4-2"></path>
                <path d="M18 5v17"></path>
                <path d="m4 6 8-4 8 4"></path>
                <path d="M6 5v17"></path>
                <circle cx="12" cy="9" r="2"></circle>
              </svg>
            </div>
            <h3 class="text-[15px] font-semibold text-gray-800">School Information</h3></div>
          <div class="space-y-3">
            <div><label class="text-[12px] font-medium text-gray-500 mb-1 block">School name</label>
              <div class="flex items-center border border-gray-200 rounded-md px-3 py-1.5 bg-white" data-field="school_name">
                <input type="text" class="text-[14px] font-medium text-gray-800 w-full outline-none school-field" data-field-name="school_name" value="<%= @patient.school_name || '' %>">
              </div>
            </div>
            <div><label class="text-[12px] font-medium text-gray-500 mb-1 block">Phone number</label>
              <div class="flex items-center border border-gray-200 rounded-md px-3 py-1.5 bg-white" data-field="school_phone_number">
                <input type="tel" class="phone-input text-[14px] font-medium text-gray-800 w-full outline-none school-field" data-field-name="school_phone_number" value="<%= @patient.school_phone_number || '' %>">
              </div>
            </div>
            <div class="pt-2 border-t border-gray-100">
              <label class="text-[12px] font-medium text-gray-500 mb-1 block">Address line 1</label>
              <div class="flex items-center border border-gray-200 rounded-md px-3 py-1.5 bg-white" data-field="school_address_line_1">
                <input type="text" class="text-[14px] font-medium text-gray-800 w-full outline-none school-field" data-field-name="school_address_line_1" value="<%= @patient.school_address_line_1 || '' %>">
              </div>
            </div>
            <div><label class="text-[12px] font-medium text-gray-500 mb-1 block">Address line 2</label>
              <div class="flex items-center border border-gray-200 rounded-md px-3 py-1.5 bg-white" data-field="school_address_line_2">
                <input type="text" class="text-[14px] font-medium text-gray-800 w-full outline-none school-field" data-field-name="school_address_line_2" value="<%= @patient.school_address_line_2 || '' %>">
              </div>
            </div>
            <div><label class="text-[12px] font-medium text-gray-500 mb-1 block">Town/City</label>
              <div class="flex items-center border border-gray-200 rounded-md px-3 py-1.5 bg-white" data-field="school_city">
                <input type="text" class="text-[14px] font-medium text-gray-800 w-full outline-none school-field" data-field-name="school_city" value="<%= @patient.school_city || '' %>">
              </div>
            </div>
            <div><label class="text-[12px] font-medium text-gray-500 mb-1 block">County</label>
              <div class="flex items-center border border-gray-200 rounded-md px-3 py-1.5 bg-white" data-field="school_county">
                <input type="text" class="text-[14px] font-medium text-gray-800 w-full outline-none school-field" data-field-name="school_county" value="<%= @patient.school_county || '' %>">
              </div>
            </div>
            <div><label class="text-[12px] font-medium text-gray-500 mb-1 block">Postcode</label>
              <div class="flex items-center border border-gray-200 rounded-md px-3 py-1.5 bg-white" data-field="school_postcode">
                <input type="text" class="text-[14px] font-medium text-gray-800 w-full outline-none school-field" data-field-name="school_postcode" value="<%= @patient.school_postcode || '' %>">
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-span-1">
        <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100 mb-4">
          <div class="flex items-center mb-4">
            <div class="w-7 h-7 rounded-full bg-indigo-100 flex items-center justify-center mr-2">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-activity h-4 w-4 text-indigo-600">
                <path d="M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2"></path>
              </svg>
            </div>
            <h3 class="text-[15px] font-semibold text-gray-800">Demographic Details</h3></div>
          <div class="space-y-3">
            <div><label class="text-[12px] font-medium text-gray-500 mb-1 block">Ethnicity</label>
              <div class="flex items-center border border-gray-200 rounded-md px-3 py-1.5 bg-white cursor-pointer demographic-field" data-field="ethnicity">
                <span class="text-[14px] font-medium text-gray-800"><%= @patient.ethnicity || 'Not specified' %></span>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-3.5 w-3.5 ml-auto text-gray-400">
                  <path d="m6 9 6 6 6-6"/>
                </svg>
              </div>
            </div>
            <div><label class="text-[12px] font-medium text-gray-500 mb-1 block">Biological sex</label>
              <div class="flex items-center border border-gray-200 rounded-md px-3 py-1.5 bg-white cursor-pointer demographic-field" data-field="biological_sex">
                <span class="text-[14px] font-medium text-gray-800"><%= @patient.biological_sex || 'Not specified' %></span>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-3.5 w-3.5 ml-auto text-gray-400">
                  <path d="m6 9 6 6 6-6"/>
                </svg>
              </div>
            </div>
            <div><label class="text-[12px] font-medium text-gray-500 mb-1 block">Gender</label>
              <div class="flex items-center border border-gray-200 rounded-md px-3 py-1.5 bg-white cursor-pointer demographic-field" data-field="gender">
                <span class="text-[14px] font-medium text-gray-800"><%= @patient.gender || 'Not specified' %></span>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-3.5 w-3.5 ml-auto text-gray-400">
                  <path d="m6 9 6 6 6-6"/>
                </svg>
              </div>
            </div>
            <div><label class="text-[12px] font-medium text-gray-500 mb-1 block">Pronouns</label>
              <div class="flex items-center border border-gray-200 rounded-md px-3 py-1.5 bg-white cursor-pointer demographic-field" data-field="pronouns">
                <span class="text-[14px] font-medium text-gray-800"><%= @patient.pronouns || 'Not specified' %></span>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-3.5 w-3.5 ml-auto text-gray-400">
                  <path d="m6 9 6 6 6-6"/>
                </svg>
              </div>
            </div>
          </div>
        </div>
        <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100" style="height: calc(45% - 8px);">
          <div class="flex items-center mb-4">
            <div class="w-7 h-7 rounded-full bg-amber-100 flex items-center justify-center mr-2">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text h-4 w-4 text-amber-600">
                <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
                <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                <path d="M10 9H8"></path>
                <path d="M16 13H8"></path>
                <path d="M16 17H8"></path>
              </svg>
            </div>
            <h3 class="text-[15px] font-semibold text-gray-800">Identification</h3></div>
          <div class="space-y-3">
            <div><label class="text-[12px] font-medium text-gray-500 mb-1 block">NI number</label>
              <div class="flex items-center border border-gray-200 rounded-md px-3 py-1.5 bg-white" data-field="ni_number">
                <input type="text" class="text-[14px] font-medium text-gray-800 w-full outline-none id-field" data-field-name="ni_number" value="<%= @patient.ni_number || '' %>">
              </div>
            </div>
            <div><label class="text-[12px] font-medium text-gray-500 mb-1 block">Insurance number</label>
              <div class="flex items-center border border-gray-200 rounded-md px-3 py-1.5 bg-white" data-field="insurance_number">
                <input type="text" class="text-[14px] font-medium text-gray-800 w-full outline-none id-field" data-field-name="insurance_number" value="<%= @patient.insurance_number || '' %>">
              </div>
            </div>
            <div><label class="text-[12px] font-medium text-gray-500 mb-1 block">Occupation</label>
              <div class="flex items-center border border-gray-200 rounded-md px-3 py-1.5 bg-white" data-field="occupation">
                <input type="text" class="text-[14px] font-medium text-gray-800 w-full outline-none id-field" data-field-name="occupation" value="<%= @patient.occupation || '' %>">
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-span-1">
        <div class="bg-white rounded-lg p-4 shadow-sm h-full border border-gray-100 mb-4">
          <div class="flex items-center mb-4">
            <div class="w-7 h-7 rounded-full bg-purple-100 flex items-center justify-center mr-2">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-purple-600">
                <path d="M14 9a2 2 0 0 1-2 2H6l-4 4V4c0-1.1.9-2 2-2h8a2 2 0 0 1 2 2v5Z"></path>
                <path d="M18 9h2a2 2 0 0 1 2 2v11l-4-4h-6a2 2 0 0 1-2-2v-1"></path>
              </svg>
            </div>
            <h3 class="text-[15px] font-semibold text-gray-800">Communication Consent</h3>
            <div class="ml-auto">
              <button id="edit-consents-btn" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 h-7 w-7 p-0 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100/80">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-square-pen h-3.5 w-3.5">
                  <path d="M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                  <path d="M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z"></path>
                </svg>
              </button>
            </div>
          </div>
          <%= render 'admin/patients/communication_consents' %>
        </div>
      </div>
      <div class="col-span-4">
        <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-100 mb-4" style="height: auto;">
          <div class="flex justify-between items-center mb-6">
            <h3 class="text-[14px] font-medium text-gray-800">NHS Information</h3>
            <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border bg-background hover:bg-accent hover:text-accent-foreground h-9 w-9 rounded-full border-gray-200">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-5 w-5">
                <path d="M5 12h14"></path>
                <path d="M12 5v14"></path>
              </svg>
            </button>
          </div>
          <div class="mb-6">
            <div class="flex items-start gap-4">
              <div class="font-medium text-gray-700 w-32 text-[14px]">NHS Number:</div>
              <div class="bg-blue-50 rounded-md px-4 py-2 text-gray-800 w-96 text-[14px]">**********</div>
            </div>
          </div>
          <div class="border-t border-gray-200 pt-4">
            <div class="overflow-auto" style="max-height: 350px;">
              <table class="w-full text-[14px] min-w-[800px]">
                <thead class="sticky top-0 bg-white z-10">
                <tr class="text-left">
                  <th class="pb-3 font-medium text-gray-600 text-[12px]">Exemption</th>
                  <th class="pb-3 font-medium text-gray-600 text-[12px]">Supporting details</th>
                  <th class="pb-3 font-medium text-gray-600 text-[12px] text-center">Evidence seen</th>
                  <th class="pb-3 font-medium text-gray-600 text-[12px]">Expires</th>
                  <th class="pb-3 font-medium text-gray-600 text-[12px]">Added</th>
                  <th class="pb-3 font-medium text-gray-600 text-[12px]">Added by</th>
                  <th class="pb-3 font-medium text-gray-600 text-[12px]">Status</th>
                  <th class="pb-3 font-medium text-gray-600 text-[12px] text-center">Actions</th>
                </tr>
                </thead>
                <tbody>
                <tr class="border-t border-gray-100 hover:bg-gray-50">
                  <td class="py-3 font-medium text-gray-800 text-[14px]">Patient under 18</td>
                  <td class="py-3 text-gray-700 text-[14px]">hello</td>
                  <td class="py-3 text-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check h-5 w-5 text-green-600 mx-auto">
                      <path d="M20 6 9 17l-5-5"></path>
                    </svg>
                  </td>
                  <td class="py-3 text-gray-700 text-[14px]">27/12/2026</td>
                  <td class="py-3 text-gray-700 text-[14px]">27/04/2025</td>
                  <td class="py-3">
                    <div class="flex items-center gap-2">
                      <span class="relative flex shrink-0 overflow-hidden rounded-full h-7 w-7"><div class="bg-gray-200 h-full w-full rounded-full"></div></span><span class="px-2 py-1 bg-amber-200 text-amber-800 rounded-md text-xs font-medium">Systems Admin</span>
                    </div>
                  </td>
                  <td class="py-3">
                    <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">Active</span>
                  </td>
                  <td class="py-3 text-center">
                    <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 h-8 w-8 text-red-500 hover:text-red-700 hover:bg-red-50 mx-auto">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash2 h-5 w-5">
                        <path d="M3 6h18"></path>
                        <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                        <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                        <line x1="10" x2="10" y1="11" y2="17"></line>
                        <line x1="14" x2="14" y1="11" y2="17"></line>
                      </svg>
                    </button>
                  </td>
                </tr>
                <tr class="border-t border-gray-100 hover:bg-gray-50">
                  <td class="py-3 font-medium text-gray-800 text-[14px]">Income Support</td>
                  <td class="py-3 text-gray-700 text-[14px]">Verified</td>
                  <td class="py-3 text-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check h-5 w-5 text-green-600 mx-auto">
                      <path d="M20 6 9 17l-5-5"></path>
                    </svg>
                  </td>
                  <td class="py-3 text-gray-700 text-[14px]">15/08/2026</td>
                  <td class="py-3 text-gray-700 text-[14px]">15/02/2025</td>
                  <td class="py-3">
                    <div class="flex items-center gap-2">
                      <span class="relative flex shrink-0 overflow-hidden rounded-full h-7 w-7"><div class="bg-gray-200 h-full w-full rounded-full"></div></span><span class="px-2 py-1 bg-amber-200 text-amber-800 rounded-md text-xs font-medium">Systems Admin</span>
                    </div>
                  </td>
                  <td class="py-3">
                    <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">Active</span>
                  </td>
                  <td class="py-3 text-center">
                    <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 h-8 w-8 text-red-500 hover:text-red-700 hover:bg-red-50 mx-auto">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash2 h-5 w-5">
                        <path d="M3 6h18"></path>
                        <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                        <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                        <line x1="10" x2="10" y1="11" y2="17"></line>
                        <line x1="14" x2="14" y1="11" y2="17"></line>
                      </svg>
                    </button>
                  </td>
                </tr>
                <tr class="border-t border-gray-100 hover:bg-gray-50">
                  <td class="py-3 font-medium text-gray-800 text-[14px]">Maternity Exemption</td>
                  <td class="py-3 text-gray-700 text-[14px]">Certificate provided</td>
                  <td class="py-3 text-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check h-5 w-5 text-green-600 mx-auto">
                      <path d="M20 6 9 17l-5-5"></path>
                    </svg>
                  </td>
                  <td class="py-3 text-gray-700 text-[14px]">10/11/2025</td>
                  <td class="py-3 text-gray-700 text-[14px]">10/05/2025</td>
                  <td class="py-3">
                    <div class="flex items-center gap-2">
                      <span class="relative flex shrink-0 overflow-hidden rounded-full h-7 w-7"><div class="bg-gray-200 h-full w-full rounded-full"></div></span><span class="px-2 py-1 bg-amber-200 text-amber-800 rounded-md text-xs font-medium">Systems Admin</span>
                    </div>
                  </td>
                  <td class="py-3">
                    <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">Active</span>
                  </td>
                  <td class="py-3 text-center">
                    <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 h-8 w-8 text-red-500 hover:text-red-700 hover:bg-red-50 mx-auto">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash2 h-5 w-5">
                        <path d="M3 6h18"></path>
                        <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                        <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                        <line x1="10" x2="10" y1="11" y2="17"></line>
                        <line x1="14" x2="14" y1="11" y2="17"></line>
                      </svg>
                    </button>
                  </td>
                </tr>
                <tr class="border-t border-gray-100 hover:bg-gray-50">
                  <td class="py-3 font-medium text-gray-800 text-[14px]">Medical Condition</td>
                  <td class="py-3 text-gray-700 text-[14px]">Diabetes</td>
                  <td class="py-3 text-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check h-5 w-5 text-green-600 mx-auto">
                      <path d="M20 6 9 17l-5-5"></path>
                    </svg>
                  </td>
                  <td class="py-3 text-gray-700 text-[14px]">No expiry</td>
                  <td class="py-3 text-gray-700 text-[14px]">05/01/2025</td>
                  <td class="py-3">
                    <div class="flex items-center gap-2">
                      <span class="relative flex shrink-0 overflow-hidden rounded-full h-7 w-7"><div class="bg-gray-200 h-full w-full rounded-full"></div></span><span class="px-2 py-1 bg-amber-200 text-amber-800 rounded-md text-xs font-medium">Systems Admin</span>
                    </div>
                  </td>
                  <td class="py-3">
                    <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">Active</span>
                  </td>
                  <td class="py-3 text-center">
                    <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 h-8 w-8 text-red-500 hover:text-red-700 hover:bg-red-50 mx-auto">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash2 h-5 w-5">
                        <path d="M3 6h18"></path>
                        <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                        <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                        <line x1="10" x2="10" y1="11" y2="17"></line>
                        <line x1="14" x2="14" y1="11" y2="17"></line>
                      </svg>
                    </button>
                  </td>
                </tr>
                <tr class="border-t border-gray-100 hover:bg-gray-50">
                  <td class="py-3 font-medium text-gray-800 text-[14px]">Tax Credit Exemption</td>
                  <td class="py-3 text-gray-700 text-[14px]">Family tax credit</td>
                  <td class="py-3 text-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check h-5 w-5 text-green-600 mx-auto">
                      <path d="M20 6 9 17l-5-5"></path>
                    </svg>
                  </td>
                  <td class="py-3 text-gray-700 text-[14px]">30/06/2026</td>
                  <td class="py-3 text-gray-700 text-[14px]">30/12/2024</td>
                  <td class="py-3">
                    <div class="flex items-center gap-2">
                      <span class="relative flex shrink-0 overflow-hidden rounded-full h-7 w-7"><div class="bg-gray-200 h-full w-full rounded-full"></div></span><span class="px-2 py-1 bg-amber-200 text-amber-800 rounded-md text-xs font-medium">Systems Admin</span>
                    </div>
                  </td>
                  <td class="py-3">
                    <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">Active</span>
                  </td>
                  <td class="py-3 text-center">
                    <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 h-8 w-8 text-red-500 hover:text-red-700 hover:bg-red-50 mx-auto">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash2 h-5 w-5">
                        <path d="M3 6h18"></path>
                        <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                        <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                        <line x1="10" x2="10" y1="11" y2="17"></line>
                        <line x1="14" x2="14" y1="11" y2="17"></line>
                      </svg>
                    </button>
                  </td>
                </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
    <style>
        @keyframes apple-edit {
            0% {
                transform: translate(0) scale(1);
            }
            25% {
                transform: translate(-2px, 0) scale(1.05);
            }
            50% {
                transform: translate(0, 0) scale(1);
            }
            75% {
                transform: translate(2px, 0) scale(1.05);
            }
            100% {
                transform: translate(0) scale(1);
            }
        }

        .apple-edit-animation {
            animation: apple-edit 1.2s cubic-bezier(0.25, 0.1, 0.25, 1) infinite;
        }

        .apple-edit-animation:hover {
            animation-play-state: paused;
        }
    </style>
  </div>
</div>
