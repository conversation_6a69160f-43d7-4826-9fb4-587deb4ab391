<%
  # International phone input component for editable fields
  # Parameters:
  # - field_name: the name of the field (e.g., 'mobile_phone')
  # - field_value: the current value of the field
  # - label: the label for the field (for accessibility)
  
  field_id = "intl_phone_#{field_name}_#{SecureRandom.hex(4)}"
  display_value = field_value.present? ? field_value : '-'
  display_class = field_value.present? ? 'font-medium text-gray-800' : 'text-gray-400'
%>

<div class="editable-field international-phone-field border border-gray-200 rounded-md px-3 py-1.5 bg-white transition-colors duration-300" data-field="<%= field_name %>">
  <span class="field-display text-[14px] <%= display_class %>"><%= display_value %></span>
  <div class="field-edit hidden w-full">
    <div class="international-phone-container">
      <input 
        type="tel" 
        id="<%= field_id %>"
        name="patient[<%= field_name %>]" 
        class="international-phone-input w-full text-[14px] font-medium text-gray-800 bg-transparent border-0 focus:outline-none focus:ring-0" 
        value="<%= field_value %>"
        data-field-name="<%= field_name %>"
        aria-label="<%= label %>"
      >
    </div>
  </div>
</div>
