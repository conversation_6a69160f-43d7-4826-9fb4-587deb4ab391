<%
  # Simple international phone input component for direct input fields
  # Parameters:
  # - field_name: the name of the field (e.g., 'school_phone_number')
  # - field_value: the current value of the field
  # - label: the label for the field (for accessibility)
  # - css_class: additional CSS classes to apply
  
  field_id = "intl_phone_#{field_name}_#{SecureRandom.hex(4)}"
  css_class ||= ''
%>

<div class="flex items-center border border-gray-200 rounded-md px-3 py-1.5 bg-white international-phone-simple-container" data-field="<%= field_name %>">
  <input 
    type="tel" 
    id="<%= field_id %>"
    class="international-phone-input-simple text-[14px] font-medium text-gray-800 w-full outline-none <%= css_class %>" 
    data-field-name="<%= field_name %>" 
    value="<%= field_value || '' %>"
    aria-label="<%= label %>"
  >
</div>
