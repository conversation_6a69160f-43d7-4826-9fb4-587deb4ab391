<!DOCTYPE html>
<html lang="en">
<head>
  <title><%= page_title %></title>
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&family=Montserrat:wght@400;500;600;700;800&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet"/>
  <link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" rel="stylesheet">
  <%= stylesheet_link_tag "https://cdn.jsdelivr.net/npm/gridstack@12.0.0/dist/gridstack.min.css", media: "all" %>
  <script defer src="https://connect-js.stripe.com/v1.0/connect.js" async></script>
  <script defer src="https://js.stripe.com/v3/" async></script>
  <script defer src="https://cdn.tiny.cloud/1/h8ksxv4rsqlbi0orn42moa9x8kh5ds8kbykaucvfdjnypo4u/tinymce/7/tinymce.min.js" referrerpolicy="origin"></script>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
  <script defer src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/intl-tel-input@25.3.0/build/css/intlTelInput.css">
  <script defer src="https://cdn.jsdelivr.net/npm/intl-tel-input@25.3.0/build/js/intlTelInput.min.js"></script>
  <script defer src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script defer src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>
  <script defer src="https://cdn.jsdelivr.net/npm/qrcodejs@1.0.0/qrcode.min.js"></script>
  <link href="https://unpkg.com/cropperjs/dist/cropper.min.css" rel="stylesheet">
  <%= csrf_meta_tags %>
  <%= csp_meta_tag %>

 <%= javascript_include_tag "application", "data-turbo-track": "reload", defer: true %>

 <% if controller_path.start_with?("admin/crm") ||
       controller_path.start_with?("admin/conversations") ||
       controller_path.start_with?("admin/patients") ||
       controller_path.start_with?("admin/prescriptions") ||
       controller_path.start_with?("admin/calendar") ||
       controller_path.start_with?("admin/treatment_plans") ||
       controller_path.start_with?("admin/treatment_plan_options") ||
       controller_path.start_with?("admin/lab_works") ||
       controller_path.start_with?("admin/lab_dockets") ||
       controller_path.start_with?("admin/signature_requests") ||
       controller_path.start_with?("admin/general_settings") ||
       controller_path.start_with?("admin/hr_management") ||
       controller_path.start_with?("admin/whatsapp_templates") ||
       controller_path.start_with?("admin/reports") ||
       controller_path.include?("/charting")  %>
  <%= stylesheet_link_tag "tailwind", "data-turbo-track": "reload" %>

  <!-- Tailwind theme for Select2 -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/erimicel/select2-tailwindcss-theme/dist/select2-tailwindcss-theme-plain.min.css">
 <% else %>
  <%= stylesheet_link_tag "stylesheets/admin/application", "data-turbo-track": "reload" %>
 <% end %>

 <script defer type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.15.6/Sortable.js"></script>
 <script defer src="https://js.pusher.com/8.2.0/pusher.min.js"></script>

</head>

<%= javascript_tag nonce: true do %>
  window._availablePractices = <%= raw(current_user.practices.map { |pr| { name: pr.name, id: pr.id } }.to_json) %>;
  <% if Current.practice_id %>
    window._currentPracticeId = <%= Current.practice_id %>;
  <% end %>
  window._currentUserId = <%= current_user.id %>;
  window._currentUserName = "<%= current_user.full_name %>";
  window._pusherApp = "<%= ENV["PUSHER_KEY"] %>";
  window._patientId = "<%= params[:patient_id] %>";
  <% if controller_name == 'charting' && params[:course_of_treatment_id].present? %>
    window._courseOfTreatmentId = "<%= params[:course_of_treatment_id] %>";
  <% elsif controller_name == 'charting' && @course_of_treatment.present? %>
    window._courseOfTreatmentId = "<%= @course_of_treatment.id %>";
  <% end %>
  window._deepgramApiKey = "<%= ENV["DEEPGRAM_API_KEY"] %>";
  window._currentPracticeId = <%= Current.practice_id.present? ? Current.practice_id : 'null' %>;
<% end %>

<% if controller_path.start_with?("admin/crm") ||
     controller_path.start_with?("admin/conversations") ||
     controller_path.start_with?("admin/patients") ||
     controller_path.start_with?("admin/prescriptions") ||
     controller_path.start_with?("admin/calendar") ||
     controller_path.start_with?("admin/treatment_plans") ||
     controller_path.start_with?("admin/treatment_plan_options") ||
     controller_path.start_with?("admin/lab_works") ||
     controller_path.start_with?("admin/lab_dockets") ||
     controller_path.start_with?("admin/signature_requests") ||
     controller_path.start_with?("admin/general_settings") ||
     controller_path.start_with?("admin/hr_management") ||
     controller_path.start_with?("admin/whatsapp_templates") ||
     controller_path.start_with?("admin/reports") ||
     controller_path.include?("/charting")  %>
<% unless current_user.lab_user? %>
  <%= render "layouts/admin/tw_navbar" %>
  <%= render "layouts/admin/notifications" %>
  <%= render "layouts/admin/actions_sidebar" %>
<%end %>
<% else %>
  <%= render "layouts/admin/navbar" %>
  <%= render "layouts/admin/notifications" %>
  <%= render "layouts/admin/actions_sidebar" %>
<% end %>


<body class="container-fluid adminbackground">
<main class="main-content bg-gray-50 flex flex-col min-h-[calc(100vh-56px)]">

  <% if flash.any? %>
    <script type="text/javascript">
      document.addEventListener("DOMContentLoaded", function() {
        <% flash.each do |key, value| %>
          <% type = key.to_s.gsub('alert', 'error').gsub('notice', 'success') %>
          <% if value.is_a?(Array) %>
            toastr['<%= type %>']('<%= value[1] %>', '<%= value[0] %>');
          <% else %>
            toastr['<%= type %>']('<%= value %>');
          <% end %>
        <% end %>
      });
    </script>
  <% end %>

  <%= yield %>
  <%= render "admin/shared/truncate_modal" %>
</main>
  <!-- Calendar Sidebar Container -->
  <div id="calendar-sidebar-container"></div>

  <!-- Shared overlay for all sidebars -->
  <div id="shared-sidebar-overlay" class="fixed inset-0 bg-black/40 backdrop-blur-sm z-[999] hidden" style="opacity: 0; transition: opacity 0.3s ease-in-out;"></div>
</body>
</html>
