// International Phone Input Styling
// Custom styles to integrate intl-tel-input with the existing design

.international-phone-container {
  position: relative;
  width: 100%;
}

.international-phone-simple-container {
  position: relative;
}

// Override intl-tel-input styles to match our design
.iti {
  width: 100%;
  
  &.iti--allow-dropdown {
    .iti__flag-container {
      border: none;
      background: transparent;
      
      .iti__selected-flag {
        border: none;
        background: transparent;
        padding: 0 8px 0 0;
        
        &:hover {
          background: transparent;
        }
        
        &:focus {
          outline: none;
          background: transparent;
        }
        
        .iti__flag {
          margin-right: 8px;
        }
        
        .iti__arrow {
          border-top-color: #6b7280;
          margin-left: 4px;
        }
      }
    }
    
    .iti__country-list {
      border: 1px solid #d1d5db;
      border-radius: 8px;
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      z-index: 9999;
      max-height: 200px;
      
      .iti__country {
        padding: 8px 12px;
        font-size: 14px;
        
        &:hover {
          background-color: #f3f4f6;
        }
        
        &.iti__highlight {
          background-color: #dbeafe;
        }
        
        .iti__flag {
          margin-right: 8px;
        }
        
        .iti__country-name {
          color: #374151;
        }
        
        .iti__dial-code {
          color: #6b7280;
        }
      }
    }
  }
  
  .iti__tel-input {
    border: none;
    background: transparent;
    font-size: 14px;
    font-weight: 500;
    color: #1f2937;
    padding-left: 0;
    
    &:focus {
      outline: none;
      box-shadow: none;
    }
    
    &::placeholder {
      color: #9ca3af;
      font-weight: normal;
    }
  }
}

// Specific styles for editable fields
.international-phone-field {
  .iti {
    .iti__tel-input {
      width: 100%;
      background: transparent;
    }
  }
  
  &.editing {
    .iti__country-list {
      z-index: 10000; // Ensure dropdown appears above other elements
    }
  }
}

// Specific styles for simple fields
.international-phone-simple-container {
  .iti {
    .iti__tel-input {
      width: 100%;
      background: transparent;
    }
  }
}

// Loading states
.international-phone-field.bg-blue-50 {
  .iti {
    .iti__tel-input {
      background: #eff6ff;
    }
  }
}

.international-phone-field.bg-red-50 {
  .iti {
    .iti__tel-input {
      background: #fef2f2;
    }
  }
}

.international-phone-field.bg-green-50 {
  .iti {
    .iti__tel-input {
      background: #f0fdf4;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .iti {
    &.iti--allow-dropdown {
      .iti__country-list {
        max-height: 150px;
        font-size: 13px;
      }
    }
  }
}

// Dark mode support (if needed in the future)
@media (prefers-color-scheme: dark) {
  .iti {
    &.iti--allow-dropdown {
      .iti__selected-flag {
        .iti__arrow {
          border-top-color: #9ca3af;
        }
      }
      
      .iti__country-list {
        background-color: #374151;
        border-color: #4b5563;
        
        .iti__country {
          &:hover {
            background-color: #4b5563;
          }
          
          &.iti__highlight {
            background-color: #1e40af;
          }
          
          .iti__country-name {
            color: #f3f4f6;
          }
          
          .iti__dial-code {
            color: #9ca3af;
          }
        }
      }
    }
    
    .iti__tel-input {
      color: #f3f4f6;
      
      &::placeholder {
        color: #6b7280;
      }
    }
  }
}
