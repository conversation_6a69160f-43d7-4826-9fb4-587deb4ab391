@import "tailwindcss";

/* Fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

// Override Font Awesome font path to use Rails asset helper for fingerprinting
$fa-font-path: "." !default;

@import "@fortawesome/fontawesome-pro/scss/fontawesome";
@import "@fortawesome/fontawesome-pro/scss/brands";
@import "@fortawesome/fontawesome-pro/scss/solid";
@import "@fortawesome/fontawesome-pro/scss/regular";
@import "@fortawesome/fontawesome-pro/scss/light";
@import "@fortawesome/fontawesome-pro/scss/thin";
@import "@fortawesome/fontawesome-pro/scss/duotone";
@import "@fortawesome/fontawesome-pro/scss/sharp-solid";
@import "@fortawesome/fontawesome-pro/scss/sharp-regular";
@import "@fortawesome/fontawesome-pro/scss/sharp-light";
@import "@fortawesome/fontawesome-pro/scss/sharp-thin";
@import "@fortawesome/fontawesome-pro/scss/sharp-duotone-solid";

@import 'trix/dist/trix';
@import "toastr/toastr";

/* CSS Animation Library */
@import "animate.css";

@import "select2/dist/css/select2.css";
@import 'tippy.js/dist/tippy.css';
@import 'tippy.js/themes/light.css';
@import 'flatpickr/dist/flatpickr.min.css';
@import "air-datepicker/air-datepicker.css";

@import "slick-carousel/slick/slick";
@import "slick-carousel/slick/slick-theme";

@import "dropzone/dist/dropzone.css";
@import "intl-tel-input/build/css/intlTelInput.css";

$border-color:     #fff;

@import "./_config";
@import "./_sweetalert2";
@import "./shared/index";
@import "./international_phone_input";

body {
  font-size: 14px;
  font-family: "Poppins", sans-serif;
}

/* Custom styles */
@import "./navbar/navbar";
@import "./navbar/privacy_screen";
@import "./general_settings/index";
@import "./patients/charting/index";
@import "./patients/perio_exams/all";
@import "./patients/account/overview";
@import "./patients/information";
@import "./patients/medical_histories/index";
@import "./patients/tabs";
@import "./patients/assets";
@import "./patients/address";
@import "./patients/pinned_notes";
@import "./conversations/index";
@import "./actions/index";
@import "./patients/payments";
@import "./patients/invoices/index";
@import "./patients/charting_appointments";
@import "./treatment_plans/index";
@import "./treatment_plan_options/index";
@import "./calendar_bookings/index";
@import "./treatment_plan_estimates/index";
@import "./hr_management/index";
@import "./lab_works/all";
@import "./lab_dockets/new";
@import "./signature_requests/all";
@import "./prescriptions/form";
@import "./reports/all";
@import "./dashboards/widgets";
@import "./crm/index";
@import "./whatsapp_templates/preview";
@import "../patients/treatment_plans/accept";

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
