// International phone input with persistent flags
document.addEventListener('DOMContentLoaded', function() {
  initializePhoneInputs();
  
  // Re-initialize when editable fields are clicked
  document.addEventListener('click', function(e) {
    const editableField = e.target.closest('.editable-field');
    if (editableField && editableField.querySelector('.phone-input')) {
      setTimeout(() => {
        initializePhoneInputs();
      }, 50);
    }
  });
  
  // Override the default field saving to handle international numbers
  document.addEventListener('blur', function(e) {
    if (e.target.classList.contains('phone-input') && e.target.intlTelInstance) {
      e.preventDefault();
      e.stopPropagation();
      saveInternationalPhoneField(e.target);
    }
  }, true);
});

function initializePhoneInputs() {
  // Find all phone inputs that haven't been initialized yet
  const phoneInputs = document.querySelectorAll('.phone-input:not([data-intl-tel-initialized])');
  
  phoneInputs.forEach(function(input) {
    // Skip if already initialized or not visible
    if (input.dataset.intlTelInitialized || !input.offsetParent) {
      return;
    }
    
    // Initialize intl-tel-input
    const iti = window.intlTelInput(input, {
      initialCountry: 'gb',
      preferredCountries: ['gb', 'us', 'ie', 'fr', 'de', 'es', 'it'],
      separateDialCode: true,
      formatOnDisplay: true,
      nationalMode: false,
      autoPlaceholder: 'aggressive',
      utilsScript: "https://cdn.jsdelivr.net/npm/intl-tel-input@25.3.1/build/js/utils.js"
    });
    
    // Mark as initialized
    input.dataset.intlTelInitialized = 'true';
    input.intlTelInstance = iti;
    
    // Update the display value when country changes
    input.addEventListener('countrychange', function() {
      updateDisplayValue(input);
    });
    
    // Handle school field saving
    if (input.classList.contains('school-field')) {
      input.addEventListener('blur', function() {
        saveSchoolField(input);
      });
    }
  });
}

function updateDisplayValue(input) {
  const iti = input.intlTelInstance;
  if (!iti) return;
  
  const editableField = input.closest('.editable-field');
  if (!editableField) return;
  
  const display = editableField.querySelector('.field-display');
  if (!display) return;
  
  const fullNumber = iti.getNumber();
  if (fullNumber && fullNumber.trim() !== '') {
    display.textContent = fullNumber;
    display.classList.remove('text-gray-400');
    display.classList.add('font-medium', 'text-gray-800');
  } else {
    display.textContent = '-';
    display.classList.add('text-gray-400');
    display.classList.remove('font-medium', 'text-gray-800');
  }
}

function saveInternationalPhoneField(input) {
  const iti = input.intlTelInstance;
  if (!iti) return;
  
  const editableField = input.closest('.editable-field');
  if (!editableField) return;
  
  const fieldName = editableField.dataset.field;
  const fullNumber = iti.getNumber();
  
  // Update the input value with the full international number
  input.value = fullNumber;
  
  // Update display immediately
  updateDisplayValue(input);
  
  // Get patient ID
  const form = document.getElementById('patient-details-form');
  if (!form) return;
  
  const patientId = form.dataset.patientId;
  
  // Show loading state
  editableField.classList.add('bg-blue-50', 'border-blue-200');
  
  // Prepare data for AJAX request
  const data = {};
  data['patient[' + fieldName + ']'] = fullNumber;
  
  // Send AJAX request using jQuery
  $.ajax({
    url: '/admin/patients/' + patientId + '/update_field',
    method: 'PATCH',
    data: data,
    dataType: 'json',
    headers: {
      'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
    },
    success: function(response) {
      if (response.success) {
        // Update display with formatted number
        updateDisplayValue(input);
        resetEditableField(editableField);
        
        // Show success feedback
        editableField.classList.add('bg-green-50', 'border-green-200');
        setTimeout(() => {
          editableField.classList.remove('bg-green-50', 'border-green-200');
        }, 1000);
      } else {
        handleFieldError(editableField, response.message || 'Failed to save');
      }
    },
    error: function(xhr) {
      let errorMessage = 'Failed to save';
      if (xhr.responseJSON && xhr.responseJSON.message) {
        errorMessage = xhr.responseJSON.message;
      }
      handleFieldError(editableField, errorMessage);
    }
  });
}

function saveSchoolField(input) {
  const iti = input.intlTelInstance;
  if (!iti) return;
  
  const fieldName = input.dataset.fieldName;
  const fullNumber = iti.getNumber();
  
  if (!fieldName) return;
  
  const patientId = document.querySelector('meta[name="patient-id"]')?.content;
  if (!patientId) return;
  
  // Save via AJAX
  const data = {
    patient: {},
    authenticity_token: document.querySelector('meta[name="csrf-token"]')?.content
  };
  data.patient[fieldName] = fullNumber;
  
  fetch(`/admin/patients/${patientId}/update_field`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRF-Token': data.authenticity_token
    },
    body: JSON.stringify(data)
  })
  .then(response => response.json())
  .then(data => {
    if (data.success && typeof toastr !== 'undefined') {
      toastr.success(`${fieldName.replace(/_/g, ' ')} updated`);
    }
  })
  .catch(error => {
    if (typeof toastr !== 'undefined') {
      toastr.error(`Failed to update ${fieldName.replace(/_/g, ' ')}`);
    }
  });
}

function handleFieldError(editableField, errorMessage) {
  // Show error state
  editableField.classList.remove('bg-blue-50', 'border-blue-200');
  editableField.classList.add('bg-red-50', 'border-red-200');

  // Show error message
  if (typeof toastr !== 'undefined') {
    toastr.error(errorMessage);
  }

  // Reset after a delay
  setTimeout(() => {
    editableField.classList.remove('bg-red-50', 'border-red-200');
    resetEditableField(editableField);
  }, 3000);
}

function resetEditableField(editableField) {
  editableField.classList.remove('editing', 'bg-blue-50', 'border-blue-200', 'bg-red-50', 'border-red-200');
  editableField.classList.add('bg-white');

  const display = editableField.querySelector('.field-display');
  const edit = editableField.querySelector('.field-edit');

  if (display && edit) {
    display.classList.remove('hidden');
    edit.classList.add('hidden');
  }
}
