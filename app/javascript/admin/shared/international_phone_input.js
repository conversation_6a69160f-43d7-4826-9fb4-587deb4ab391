// International Phone Input functionality
// Handles initialization and integration with existing editable fields system

import intlTelInput from 'intl-tel-input';

document.addEventListener('DOMContentLoaded', function() {
  initializeInternationalPhoneInputs();
  setupInternationalPhoneEditingHandlers();
});

/**
 * Initialize international phone inputs for all phone fields
 */
function initializeInternationalPhoneInputs() {
  // Initialize for editable fields (main patient details)
  document.querySelectorAll('.international-phone-input').forEach(input => {
    initializePhoneInput(input, {
      initialCountry: 'gb', // UK as default
      preferredCountries: ['gb', 'us', 'ie', 'fr', 'de', 'es', 'it'],
      separateDialCode: true,
      formatOnDisplay: true,
      nationalMode: false,
      autoPlaceholder: 'aggressive',
      customPlaceholder: function(selectedCountryPlaceholder, selectedCountryData) {
        return selectedCountryPlaceholder;
      }
    });
  });

  // Initialize for simple fields (school phone, etc.)
  document.querySelectorAll('.international-phone-input-simple').forEach(input => {
    initializePhoneInput(input, {
      initialCountry: 'gb', // UK as default
      preferredCountries: ['gb', 'us', 'ie', 'fr', 'de', 'es', 'it'],
      separateDialCode: true,
      formatOnDisplay: true,
      nationalMode: false,
      autoPlaceholder: 'aggressive',
      customPlaceholder: function(selectedCountryPlaceholder, selectedCountryData) {
        return selectedCountryPlaceholder;
      }
    });
  });
}

/**
 * Initialize a single phone input with intl-tel-input
 */
function initializePhoneInput(input, options) {
  if (input.dataset.intlTelInitialized) {
    return; // Already initialized
  }

  const iti = intlTelInput(input, options);
  
  // Store the instance for later use
  input.dataset.intlTelInitialized = 'true';
  input.intlTelInputInstance = iti;

  // Handle country change to update the display
  input.addEventListener('countrychange', function() {
    updatePhoneDisplay(input);
  });

  // Handle input changes
  input.addEventListener('input', function() {
    updatePhoneDisplay(input);
  });

  // Handle blur for simple fields (auto-save)
  if (input.classList.contains('international-phone-input-simple')) {
    input.addEventListener('blur', function() {
      saveSimplePhoneField(input);
    });
  }

  return iti;
}

/**
 * Update the display value for editable phone fields
 */
function updatePhoneDisplay(input) {
  if (!input.intlTelInputInstance) return;

  const field = input.closest('.international-phone-field');
  if (!field) return;

  const display = field.querySelector('.field-display');
  if (!display) return;

  const fullNumber = input.intlTelInputInstance.getNumber();
  const isValid = input.intlTelInputInstance.isValidNumber();
  
  if (fullNumber && fullNumber.trim() !== '') {
    display.textContent = fullNumber;
    display.classList.remove('text-gray-400');
    display.classList.add('font-medium', 'text-gray-800');
  } else {
    display.textContent = '-';
    display.classList.add('text-gray-400');
    display.classList.remove('font-medium', 'text-gray-800');
  }
}

/**
 * Setup handlers for international phone editing integration
 */
function setupInternationalPhoneEditingHandlers() {
  // Override the existing editable field click handler for phone fields
  document.addEventListener('click', function(e) {
    const phoneField = e.target.closest('.international-phone-field');
    if (!phoneField) return;

    // Prevent default editable field behavior
    e.stopPropagation();
    
    if (phoneField.classList.contains('editing')) return;

    phoneField.classList.add('editing');

    // Hide display, show edit field
    const display = phoneField.querySelector('.field-display');
    const edit = phoneField.querySelector('.field-edit');

    if (display && edit) {
      display.classList.add('hidden');
      edit.classList.remove('hidden');

      // Focus the phone input
      const input = edit.querySelector('.international-phone-input');
      if (input) {
        // Initialize if not already done
        if (!input.dataset.intlTelInitialized) {
          initializePhoneInput(input, {
            initialCountry: 'gb',
            preferredCountries: ['gb', 'us', 'ie', 'fr', 'de', 'es', 'it'],
            separateDialCode: true,
            formatOnDisplay: true,
            nationalMode: false,
            autoPlaceholder: 'aggressive'
          });
        }
        
        setTimeout(() => {
          input.focus();
        }, 100);
      }
    }
  });

  // Handle saving on blur and enter key for international phone inputs
  document.addEventListener('blur', function(e) {
    if (e.target.classList.contains('international-phone-input')) {
      saveInternationalPhoneField(e.target);
    }
  }, true);

  document.addEventListener('keydown', function(e) {
    if (e.target.classList.contains('international-phone-input') && e.key === 'Enter') {
      e.preventDefault();
      e.target.blur(); // Trigger blur event which will save
    }
  });
}

/**
 * Save an international phone field value via AJAX
 */
function saveInternationalPhoneField(input) {
  const field = input.closest('.international-phone-field');
  if (!field) return;

  const fieldName = field.dataset.field;
  const form = document.getElementById('patient-details-form');
  if (!form) return;
  
  const patientId = form.dataset.patientId;
  const iti = input.intlTelInputInstance;
  
  if (!iti) return;

  // Get the full international number
  const fullNumber = iti.getNumber();
  const isValid = iti.isValidNumber();
  
  // Store original value to revert if needed
  const display = field.querySelector('.field-display');
  const originalDisplayValue = display.textContent.trim();

  // Don't save if nothing changed
  if ((fullNumber === '' && originalDisplayValue === '-') || 
      (fullNumber && originalDisplayValue === fullNumber)) {
    resetInternationalPhoneField(field);
    return;
  }

  // Show loading state
  field.classList.remove('bg-white');
  field.classList.add('bg-blue-50', 'border-blue-200');
  input.classList.add('bg-blue-50');

  // Prepare data for AJAX request
  const data = {};
  data['patient[' + fieldName + ']'] = fullNumber;

  // Send AJAX request using jQuery
  $.ajax({
    url: '/admin/patients/' + patientId + '/update_field',
    method: 'PATCH',
    data: data,
    dataType: 'json',
    headers: {
      'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
    },
    success: function(response) {
      if (response.success) {
        // Update display with formatted number
        updatePhoneDisplay(input);
        resetInternationalPhoneField(field);
        
        // Show success feedback
        field.classList.add('bg-green-50', 'border-green-200');
        setTimeout(() => {
          field.classList.remove('bg-green-50', 'border-green-200');
        }, 1000);
      } else {
        handleInternationalPhoneFieldError(field, input, originalDisplayValue, response.message || 'Failed to save');
      }
    },
    error: function(xhr) {
      let errorMessage = 'Failed to save';
      if (xhr.responseJSON && xhr.responseJSON.message) {
        errorMessage = xhr.responseJSON.message;
      }
      handleInternationalPhoneFieldError(field, input, originalDisplayValue, errorMessage);
    }
  });
}

/**
 * Save a simple phone field (like school phone)
 */
function saveSimplePhoneField(input) {
  const fieldName = input.getAttribute('data-field-name');
  const iti = input.intlTelInputInstance;
  
  if (!iti || !fieldName) return;

  const fullNumber = iti.getNumber();
  const patientId = document.querySelector('meta[name="patient-id"]')?.content;
  
  if (!patientId) return;

  // Create data object for this specific field
  const data = {};
  data.patient = {};
  data.patient[fieldName] = fullNumber;

  // Add CSRF token
  data.authenticity_token = $('meta[name="csrf-token"]').attr('content');

  // Make the AJAX request
  $.ajax({
    url: `/admin/patients/${patientId}/update_field`,
    method: 'PATCH',
    data: data,
    success: function(response) {
      if (response.success) {
        toastr.success(`${fieldName.replace(/_/g, ' ')} updated`);
      } else {
        toastr.error(`Failed to update ${fieldName.replace(/_/g, ' ')}`);
      }
    },
    error: function() {
      toastr.error(`Failed to update ${fieldName.replace(/_/g, ' ')}`);
    }
  });
}

/**
 * Handle errors when saving international phone fields
 */
function handleInternationalPhoneFieldError(field, input, originalValue, errorMessage) {
  // Show error state
  field.classList.remove('bg-blue-50', 'border-blue-200');
  field.classList.add('bg-red-50', 'border-red-200');
  input.classList.remove('bg-blue-50');
  input.classList.add('bg-red-50');

  // Show error message
  if (typeof toastr !== 'undefined') {
    toastr.error(errorMessage);
  }

  // Reset after a delay
  setTimeout(() => {
    field.classList.remove('bg-red-50', 'border-red-200');
    input.classList.remove('bg-red-50');
    resetInternationalPhoneField(field);
  }, 3000);
}

/**
 * Reset an international phone field to display mode
 */
function resetInternationalPhoneField(field) {
  field.classList.remove('editing', 'bg-blue-50', 'border-blue-200', 'bg-red-50', 'border-red-200');
  field.classList.add('bg-white');

  const display = field.querySelector('.field-display');
  const edit = field.querySelector('.field-edit');
  const input = field.querySelector('.international-phone-input');

  if (display && edit) {
    display.classList.remove('hidden');
    edit.classList.add('hidden');
  }

  if (input) {
    input.classList.remove('bg-blue-50', 'bg-red-50');
  }
}

// Export functions for use in other modules
window.InternationalPhoneInput = {
  initialize: initializeInternationalPhoneInputs,
  initializePhoneInput: initializePhoneInput,
  updatePhoneDisplay: updatePhoneDisplay,
  resetField: resetInternationalPhoneField
};
