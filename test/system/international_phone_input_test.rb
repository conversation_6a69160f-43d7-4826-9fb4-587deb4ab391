# frozen_string_literal: true

require "application_system_test_case"

class InternationalPhoneInputTest < ApplicationSystemTestCase
  setup do
    @user = users(:admin)
    @patient = patients(:patient_one)
    sign_in @user
  end

  test "international phone input displays correctly on patient details page" do
    visit admin_patient_path(@patient)
    
    # Check that the international phone input containers are present
    assert_selector '.international-phone-field[data-field="mobile_phone"]'
    assert_selector '.international-phone-field[data-field="alternative_phone"]'
    assert_selector '.international-phone-field[data-field="work_phone"]'
    assert_selector '.international-phone-field[data-field="emergency_contact_number"]'
    
    # Check that the phone input elements are present but hidden initially
    assert_selector '.international-phone-input', visible: false, count: 4
  end

  test "clicking on phone field shows international phone input" do
    visit admin_patient_path(@patient)
    
    # Click on the mobile phone field
    mobile_phone_field = find('.international-phone-field[data-field="mobile_phone"]')
    mobile_phone_field.click
    
    # Check that the field enters editing mode
    assert mobile_phone_field.has_css?('.editing')
    
    # Check that the input becomes visible
    assert_selector '.international-phone-input', visible: true
  end

  test "international phone input has correct attributes" do
    visit admin_patient_path(@patient)
    
    # Click on mobile phone field to show input
    find('.international-phone-field[data-field="mobile_phone"]').click
    
    # Check that the input has the correct attributes
    phone_input = find('.international-phone-input')
    assert_equal 'tel', phone_input[:type]
    assert phone_input[:id].start_with?('intl_phone_mobile_phone_')
    assert_equal 'patient[mobile_phone]', phone_input[:name]
    assert_equal 'mobile_phone', phone_input['data-field-name']
  end

  test "international phone input on additional details page" do
    visit admin_patient_additional_details_path(@patient)
    
    # Check that the simple international phone input is present for school phone
    assert_selector '.international-phone-simple-container[data-field="school_phone_number"]'
    assert_selector '.international-phone-input-simple'
    
    # Check that the input has correct attributes
    school_phone_input = find('.international-phone-input-simple')
    assert_equal 'tel', school_phone_input[:type]
    assert school_phone_input[:id].start_with?('intl_phone_school_phone_number_')
    assert_equal 'school_phone_number', school_phone_input['data-field-name']
  end

  private

  def sign_in(user)
    visit new_user_session_path
    fill_in 'Email', with: user.email
    fill_in 'Password', with: 'password'
    click_button 'Sign in'
  end
end
