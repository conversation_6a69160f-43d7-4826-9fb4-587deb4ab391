# frozen_string_literal: true

require 'test_helper'

class InternationalPhoneUpdateTest < ActionDispatch::IntegrationTest
  setup do
    @user = users(:admin)
    @patient = patients(:patient_one)
    sign_in @user
  end

  test "can update mobile phone with international number" do
    international_number = "+33123456789" # French number
    
    patch admin_patient_update_field_path(@patient), 
          params: { patient: { mobile_phone: international_number } },
          headers: { 'Accept' => 'application/json' }
    
    assert_response :success
    
    response_data = JSON.parse(response.body)
    assert response_data['success']
    assert_equal international_number, response_data['patient']['mobile_phone']
    
    @patient.reload
    assert_equal international_number, @patient.mobile_phone
  end

  test "can update alternative phone with international number" do
    international_number = "+49123456789" # German number
    
    patch admin_patient_update_field_path(@patient), 
          params: { patient: { alternative_phone: international_number } },
          headers: { 'Accept' => 'application/json' }
    
    assert_response :success
    
    response_data = JSON.parse(response.body)
    assert response_data['success']
    assert_equal international_number, response_data['patient']['alternative_phone']
    
    @patient.reload
    assert_equal international_number, @patient.alternative_phone
  end

  test "can update work phone with international number" do
    international_number = "+**********" # US number
    
    patch admin_patient_update_field_path(@patient), 
          params: { patient: { work_phone: international_number } },
          headers: { 'Accept' => 'application/json' }
    
    assert_response :success
    
    response_data = JSON.parse(response.body)
    assert response_data['success']
    assert_equal international_number, response_data['patient']['work_phone']
    
    @patient.reload
    assert_equal international_number, @patient.work_phone
  end

  test "can update emergency contact number with international number" do
    international_number = "+353123456789" # Irish number
    
    patch admin_patient_update_field_path(@patient), 
          params: { patient: { emergency_contact_number: international_number } },
          headers: { 'Accept' => 'application/json' }
    
    assert_response :success
    
    response_data = JSON.parse(response.body)
    assert response_data['success']
    assert_equal international_number, response_data['patient']['emergency_contact_number']
    
    @patient.reload
    assert_equal international_number, @patient.emergency_contact_number
  end

  test "can update school phone number with international number" do
    international_number = "+34123456789" # Spanish number
    
    patch admin_patient_update_field_path(@patient), 
          params: { patient: { school_phone_number: international_number } },
          headers: { 'Accept' => 'application/json' }
    
    assert_response :success
    
    response_data = JSON.parse(response.body)
    assert response_data['success']
    assert_equal international_number, response_data['patient']['school_phone_number']
    
    @patient.reload
    assert_equal international_number, @patient.school_phone_number
  end

  test "can clear phone number by setting empty string" do
    # First set a number
    @patient.update!(mobile_phone: "+44123456789")
    
    # Then clear it
    patch admin_patient_update_field_path(@patient), 
          params: { patient: { mobile_phone: "" } },
          headers: { 'Accept' => 'application/json' }
    
    assert_response :success
    
    response_data = JSON.parse(response.body)
    assert response_data['success']
    assert_equal "", response_data['patient']['mobile_phone']
    
    @patient.reload
    assert_equal "", @patient.mobile_phone
  end

  test "handles UK numbers correctly" do
    uk_number = "+44**********"
    
    patch admin_patient_update_field_path(@patient), 
          params: { patient: { mobile_phone: uk_number } },
          headers: { 'Accept' => 'application/json' }
    
    assert_response :success
    
    response_data = JSON.parse(response.body)
    assert response_data['success']
    assert_equal uk_number, response_data['patient']['mobile_phone']
    
    @patient.reload
    assert_equal uk_number, @patient.mobile_phone
  end

  private

  def sign_in(user)
    post user_session_path, params: {
      user: {
        email: user.email,
        password: 'password'
      }
    }
  end
end
